<?php

namespace App\Repositories;

use App\Models\Audit;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class AuditRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return Audit::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'event' => '=',
        'auditable_type' => '=',
        'auditable_id' => '=',
        'user_type' => '=',
        'user_id' => '=',
        'ip_address' => 'like',
        'url' => 'like',
        'tags' => 'like',
        'created_at' => 'between'
    ];

    /**
     * Get audits for a specific model.
     */
    public function getAuditsForModel($modelType, $modelId, $perPage = 15)
    {
        return $this->model
            ->where('auditable_type', $modelType)
            ->where('auditable_id', $modelId)
            ->with(['user'])
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get audits by user.
     */
    public function getAuditsByUser($userId, $userType = null, $perPage = 15)
    {
        $query = $this->model->where('user_id', $userId);
        
        if ($userType) {
            $query = $query->where('user_type', $userType);
        }
        
        return $query->with(['user', 'auditable'])
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get audits by event type.
     */
    public function getAuditsByEvent($event, $perPage = 15)
    {
        return $this->model
            ->where('event', $event)
            ->with(['user', 'auditable'])
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get audits by date range.
     */
    public function getAuditsByDateRange($startDate, $endDate, $perPage = 15)
    {
        return $this->model
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'auditable'])
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get audit statistics.
     */
    public function getAuditStatistics($startDate = null, $endDate = null)
    {
        $query = $this->model->newQuery();
        
        if ($startDate && $endDate) {
            $query = $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return [
            'total_audits' => $query->count(),
            'by_event' => $query->selectRaw('event, COUNT(*) as count')
                ->groupBy('event')
                ->pluck('count', 'event')
                ->toArray(),
            'by_model' => $query->selectRaw('auditable_type, COUNT(*) as count')
                ->groupBy('auditable_type')
                ->orderByDesc('count')
                ->limit(10)
                ->pluck('count', 'auditable_type')
                ->toArray(),
            'by_user_type' => $query->selectRaw('user_type, COUNT(*) as count')
                ->groupBy('user_type')
                ->pluck('count', 'user_type')
                ->toArray(),
            'recent_activity' => $query->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderByDesc('date')
                ->limit(30)
                ->pluck('count', 'date')
                ->toArray()
        ];
    }

    /**
     * Get most active users.
     */
    public function getMostActiveUsers($limit = 10, $startDate = null, $endDate = null)
    {
        $query = $this->model->newQuery();
        
        if ($startDate && $endDate) {
            $query = $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return $query->selectRaw('user_id, user_type, COUNT(*) as audit_count')
            ->whereNotNull('user_id')
            ->groupBy('user_id', 'user_type')
            ->orderByDesc('audit_count')
            ->limit($limit)
            ->with(['user'])
            ->get();
    }

    /**
     * Get most audited models.
     */
    public function getMostAuditedModels($limit = 10, $startDate = null, $endDate = null)
    {
        $query = $this->model->newQuery();
        
        if ($startDate && $endDate) {
            $query = $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return $query->selectRaw('auditable_type, COUNT(*) as audit_count')
            ->groupBy('auditable_type')
            ->orderByDesc('audit_count')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                $className = class_basename($item->auditable_type);
                $item->model_name = preg_replace('/(?<!^)[A-Z]/', ' $0', $className);
                return $item;
            });
    }

    /**
     * Search audits with advanced filters.
     */
    public function searchAudits($filters = [], $perPage = 15)
    {
        $query = $this->model->newQuery();

        // Filter by event
        if (!empty($filters['event'])) {
            $query = $query->where('event', $filters['event']);
        }

        // Filter by model type
        if (!empty($filters['model_type'])) {
            $query = $query->where('auditable_type', $filters['model_type']);
        }

        // Filter by user
        if (!empty($filters['user_id'])) {
            $query = $query->where('user_id', $filters['user_id']);
        }

        // Filter by user type
        if (!empty($filters['user_type'])) {
            $query = $query->where('user_type', $filters['user_type']);
        }

        // Filter by date range
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query = $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }

        // Filter by IP address
        if (!empty($filters['ip_address'])) {
            $query = $query->where('ip_address', 'like', '%' . $filters['ip_address'] . '%');
        }

        // Search in changes
        if (!empty($filters['search'])) {
            $query = $query->where(function ($q) use ($filters) {
                $q->where('old_values', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('new_values', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('url', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->with(['user', 'auditable'])
            ->latest()
            ->paginate($perPage);
    }
}
