<?php

namespace App\Http\Controllers;

use App\Http\Requests\AuditFilterRequest;
use App\Http\Resources\AuditResource;
use App\Repositories\AuditRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AuditController extends Controller
{
    private AuditRepository $repository;

    public function __construct(AuditRepository $repository)
    {
        $this->repository = $repository;
        $this->middleware('jwt.auth');
    }

    /**
     * Get all audits with pagination and filters.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $filters = $request->only([
            'event',
            'model_type',
            'user_id',
            'user_type',
            'start_date',
            'end_date',
            'ip_address',
            'search'
        ]);

        $perPage = $request->input('perPage', 15);

        $audits = $this->repository->searchAudits($filters, $perPage);

        return AuditResource::collection($audits);
    }

    /**
     * Get audits for a specific model.
     */
    public function getModelAudits(Request $request, string $modelType, int $modelId): AnonymousResourceCollection
    {
        $perPage = $request->input('perPage', 15);

        // Convert model type to full class name
        $fullModelType = 'App\\Models\\' . ucfirst($modelType);

        $audits = $this->repository->getAuditsForModel($fullModelType, $modelId, $perPage);

        return AuditResource::collection($audits);
    }

    /**
     * Get audits by user.
     */
    public function getUserAudits(Request $request, int $userId): AnonymousResourceCollection
    {
        $userType = $request->input('user_type');
        $perPage = $request->input('perPage', 15);

        $audits = $this->repository->getAuditsByUser($userId, $userType, $perPage);

        return AuditResource::collection($audits);
    }

    /**
     * Get current user's audits.
     */
    public function getMyAudits(Request $request): AnonymousResourceCollection
    {
        $user = Auth::guard('api')->user();
        $perPage = $request->input('perPage', 15);

        $userType = get_class($user);
        $audits = $this->repository->getAuditsByUser($user->id, $userType, $perPage);

        return AuditResource::collection($audits);
    }

    /**
     * Get audits by event type.
     */
    public function getEventAudits(Request $request, string $event): AnonymousResourceCollection
    {
        $perPage = $request->input('perPage', 15);

        $audits = $this->repository->getAuditsByEvent($event, $perPage);

        return AuditResource::collection($audits);
    }

    /**
     * Get audit statistics.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        if ($startDate) {
            $startDate = Carbon::parse($startDate)->startOfDay();
        }

        if ($endDate) {
            $endDate = Carbon::parse($endDate)->endOfDay();
        }

        $statistics = $this->repository->getAuditStatistics($startDate, $endDate);

        return response()->json([
            'data' => $statistics
        ]);
    }

    /**
     * Get most active users.
     */
    public function getMostActiveUsers(Request $request): JsonResponse
    {
        $limit = $request->input('limit', 10);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        if ($startDate) {
            $startDate = Carbon::parse($startDate)->startOfDay();
        }

        if ($endDate) {
            $endDate = Carbon::parse($endDate)->endOfDay();
        }

        $users = $this->repository->getMostActiveUsers($limit, $startDate, $endDate);

        $formattedUsers = $users->map(function ($item) {
            $userData = [
                'user_id' => $item->user_id,
                'user_type' => $item->user_type,
                'audit_count' => $item->audit_count,
                'user_name' => 'Unknown User'
            ];

            if ($item->user) {
                if ($item->user instanceof \App\Models\Admin) {
                    $userData['user_name'] = $item->user->firstname . ' ' . $item->user->lastname;
                    $userData['email'] = $item->user->email;
                    $userData['type'] = 'admin';
                } elseif ($item->user instanceof \App\Models\Client) {
                    $userData['user_name'] = $item->user->is_moral
                        ? $item->user->society_name
                        : $item->user->firstname . ' ' . $item->user->lastname;
                    $userData['email'] = $item->user->email;
                    $userData['type'] = 'client';
                }
            }

            return $userData;
        });

        return response()->json([
            'data' => $formattedUsers
        ]);
    }

    /**
     * Get most audited models.
     */
    public function getMostAuditedModels(Request $request): JsonResponse
    {
        $limit = $request->input('limit', 10);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        if ($startDate) {
            $startDate = Carbon::parse($startDate)->startOfDay();
        }

        if ($endDate) {
            $endDate = Carbon::parse($endDate)->endOfDay();
        }

        $models = $this->repository->getMostAuditedModels($limit, $startDate, $endDate);

        return response()->json([
            'data' => $models
        ]);
    }

    /**
     * Get recent activity.
     */
    public function getRecentActivity(Request $request): AnonymousResourceCollection
    {
        $limit = $request->input('limit', 20);

        $audits = $this->repository
            ->with(['user', 'auditable'])
            ->latest()
            ->limit($limit)
            ->get();

        return AuditResource::collection($audits);
    }

    /**
     * Get audit details by ID.
     */
    public function show(int $id): AuditResource
    {
        $audit = $this->repository->with(['user', 'auditable'])->find($id);

        if (!$audit) {
            abort(404, 'Audit not found');
        }

        return new AuditResource($audit);
    }

    /**
     * Get available model types for filtering.
     */
    public function getModelTypes(): JsonResponse
    {
        $modelClass = $this->repository->model();
        $modelTypes = (new $modelClass)
            ->select('auditable_type')
            ->distinct()
            ->pluck('auditable_type')
            ->map(function ($type) {
                return [
                    'value' => $type,
                    'label' => preg_replace('/(?<!^)[A-Z]/', ' $0', class_basename($type))
                ];
            })
            ->values();

        return response()->json([
            'data' => $modelTypes
        ]);
    }

    /**
     * Get available event types for filtering.
     */
    public function getEventTypes(): JsonResponse
    {
        $modelClass = $this->repository->model();
        $eventTypes = (new $modelClass)
            ->select('event')
            ->distinct()
            ->pluck('event')
            ->map(function ($event) {
                $labels = [
                    'created' => 'Created',
                    'updated' => 'Updated',
                    'deleted' => 'Deleted',
                    'restored' => 'Restored'
                ];

                return [
                    'value' => $event,
                    'label' => $labels[$event] ?? ucfirst($event)
                ];
            })
            ->values();

        return response()->json([
            'data' => $eventTypes
        ]);
    }
}
