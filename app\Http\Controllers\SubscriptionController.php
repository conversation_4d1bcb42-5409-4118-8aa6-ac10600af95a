<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSubscriptionRequest;
use App\Http\Requests\UpdateSubscriptionRequest;
use App\Http\Resources\SubscriptionResource;
use App\Models\Admin;
use App\Models\AffectationAgent;
use App\Models\Client;
use App\Models\SalePeriod;
use App\Models\Subscription;
use App\Models\Trip;
use App\Models\Station;
use App\Models\Line;
use App\Repositories\SubscriptionRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SubscriptionController extends Controller
{
    private SubscriptionRepository $repository;

    public function __construct(SubscriptionRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Subscription::class, 'subscription');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $subscriptions = $this->repository
            ->with([
                'subsType',
                'client.clientType',
                'client.governorate',
                'client.delegation',
                'client.establishment',
                'client.degree',
                'paymentMethod',
                'trip',
                'trip.tariffOptions',
                'trip.tariffOptions.tariffBase',
                'periodicity'
            ])
            ->latest()
            ->paginate($request->input('perPage'));

        foreach ($subscriptions as $subscription) {
            $latestTransaction = $subscription->transactions()->latest()->first();
            if ($latestTransaction) {
                $subscription->setRelation('latestTransaction', $latestTransaction);
            } else {
                $subscription->setRelation('latestTransaction', null);
            }
        }

        return SubscriptionResource::collection($subscriptions);
    }

    public function all(): AnonymousResourceCollection
    {
        $subscriptions = $this->repository
            ->with(['subsType', 'client.clientType', 'client.governorate', 'client.delegation', 'client.establishment', 'client.degree', 'paymentMethod', 'trip', 'periodicity'])
            ->all();

        // Load only the latest transaction for each subscription
        foreach ($subscriptions as $subscription) {
            $latestTransaction = $subscription->transactions()->latest()->first();
            if ($latestTransaction) {
                $subscription->setRelation('latestTransaction', $latestTransaction);
            } else {
                $subscription->setRelation('latestTransaction', null);
            }
        }

        return SubscriptionResource::collection($subscriptions);
    }

    public function store(StoreSubscriptionRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $clientFields = [
                'firstname', 'lastname', 'society_name', 'legal_representative',
                'identity_number', 'phone', 'email', 'address', 'dob',
                'id_governorate', 'id_delegation', 'id_establishment', 'id_degree',
                'is_moral', 'is_withTVA'
            ];

            $clientData = array_intersect_key($data, array_flip($clientFields));

            foreach ($clientFields as $field) {
                unset($data[$field]);
            }

            $client = Client::create($clientData);
            $data['id_client'] = $client->id;
            $data['id_agent'] = $request->user()->id;
            $admin =  $request->user();

            $affectation = AffectationAgent::where('id_agent', $admin->id)
            ->whereHas('salePeriod', function ($query) {
                $query->where('date_start', '<=', now())->where('date_end', '>=', now());
            })
            ->first();

            if (!$affectation) {
                return response()->json([
                    'message' => __('messages.subscription.affectation_not_found'),
                ], 422);
            }

            $data['id_affectation'] = $affectation?->id;
            $data['id_sale_point'] = $affectation?->salePoint?->id;
            $data['id_sale_period'] = $affectation?->salePeriod?->id;

            if (isset($data['id_station_start']) && isset($data['id_station_end']) && isset($data['id_line'])) {
                $trip = Trip::where('id_station_start', $data['id_station_start'])
                    ->where('id_station_end', $data['id_station_end'])
                    ->where('inter_station', false)
                    ->where('id_line', $data['id_line'])
                    ->first();

                if ($trip) {
                    $data['id_trip'] = $trip->id;
                }

                unset($data['id_station_start']);
                unset($data['id_station_end']);
                unset($data['id_line']);
            }

            foreach (['is_reversed', 'is_social_affair', 'hasVacances'] as $boolField) {
                if (isset($data[$boolField]) && is_string($data[$boolField])) {
                    $data[$boolField] = filter_var($data[$boolField], FILTER_VALIDATE_BOOLEAN);
                }
            }

            if (isset($data['rest_days'])) {
                if (is_string($data['rest_days'])) {
                    $data['rest_days'] = array_map(
                        'trim',
                        explode(',', trim(str_replace(['"', '[', ']'], '', $data['rest_days'])))
                    );
                }
                elseif (is_array($data['rest_days'])) {
                    $data['rest_days'] = array_map(
                        'trim',
                        array_map('strval', $data['rest_days'])
                    );
                }
                else {
                    $data['rest_days'] = [];
                }
            }

            if (isset($data['subs_number']) && is_string($data['subs_number'])) {
                $data['subs_number'] = (int)$data['subs_number'];
            }

            $subscription = $this->repository->create($data);

            return response()->json([
                'message' => $subscription,
                'data' => new SubscriptionResource($subscription)
            ], 201);

        } catch (\Exception $e) {
            Log::error('Subscription creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data ?? null
            ]);

            return response()->json([
                'message' => 'Error creating subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Subscription $subscription): SubscriptionResource
    {
        $subscription->load(['subsType', 'client.clientType', 'client.governorate', 'client.delegation', 'client.establishment', 'client.degree', 'paymentMethod', 'trip', 'periodicity', 'parentSubscription', 'childSubscriptions', 'subsCards']);

        // Load the latest transaction
        $latestTransaction = $subscription->transactions()->latest()->first();
        if ($latestTransaction) {
            $subscription->setRelation('latestTransaction', $latestTransaction);
        } else {
            $subscription->setRelation('latestTransaction', null);
        }

        return new SubscriptionResource($subscription);
    }

    public function update(UpdateSubscriptionRequest $request, Subscription $subscription): JsonResponse
    {
        try {
            // Start database transaction
            DB::beginTransaction();

            $data = $request->validated();

            // Add the authenticated user's ID as agent
            $data['id_agent'] = $request->user()->id;

            // Check if subscription is paid and change status to PENDING
            if ($subscription->status === 'PAYED') {
                $data['status'] = 'PENDING';

                // Find and update the latest transaction to canceled
                $latestTransaction = $subscription->transactions()->latest()->first();
                if ($latestTransaction) {
                    $latestTransaction->update(['status' => 'canceled']);
                }
            }

            $clientFields = [
                'firstname', 'lastname', 'society_name', 'legal_representative',
                'identity_number', 'phone', 'email', 'address', 'dob',
                'id_governorate', 'id_delegation', 'id_establishment', 'id_degree',
                'is_moral', 'is_withTVA'
            ];

            $clientData = array_intersect_key($data, array_flip($clientFields));

            // If client data is provided, update the client
            if (!empty($clientData)) {
                // Remove client fields from subscription data
                foreach ($clientFields as $field) {
                    unset($data[$field]);
                }

                // Update the client
                if ($subscription->id_client) {
                    $client = Client::find($subscription->id_client);
                    if ($client) {
                        $client->update($clientData);
                    }
                }
            }

            if ($request->hasFile('photo')) {
                $data['photo'] = $request->file('photo');
            }

            if (isset($data['id_station_start']) && isset($data['id_station_end']) && isset($data['id_line'])) {
                $trip = Trip::where('id_station_start', $data['id_station_start'])
                    ->where('id_station_end', $data['id_station_end'])
                    ->where('inter_station', false)
                    ->where('id_line', $data['id_line'])
                    ->first();

                if ($trip) {
                    $data['id_trip'] = $trip->id;
                } else {
                    $startStation = Station::find($data['id_station_start']);
                    $endStation = Station::find($data['id_station_end']);
                    $line = Line::find($data['id_line']);

                    if ($startStation && $endStation && $line) {
                        $newTrip = new Trip();
                        $newTrip->id_station_start = $data['id_station_start'];
                        $newTrip->id_station_end = $data['id_station_end'];
                        $newTrip->id_line = $data['id_line'];
                        $newTrip->nom_fr = 'De ' . $startStation->nom_fr . ' à ' . $endStation->nom_fr . ' (Ligne ' . $line->nom_fr . ')';
                        $newTrip->nom_en = 'From ' . $startStation->nom_en . ' to ' . $endStation->nom_en . ' (Line ' . $line->nom_en . ')';
                        $newTrip->nom_ar = $startStation->nom_ar . ' إلى ' . $endStation->nom_ar . ' (خط ' . $line->nom_ar . ')';
                        $newTrip->status = true;
                        $newTrip->inter_station = false;
                        $newTrip->number_of_km = 0;
                        $newTrip->save();

                        $data['id_trip'] = $newTrip->id;
                    }
                }

                unset($data['id_station_start']);
                unset($data['id_station_end']);
                unset($data['id_line']);
            }

            foreach (['is_reversed', 'is_social_affair', 'hasVacances'] as $boolField) {
                if (isset($data[$boolField]) && is_string($data[$boolField])) {
                    $data[$boolField] = filter_var($data[$boolField], FILTER_VALIDATE_BOOLEAN);
                }
            }

            if (isset($data['rest_days'])) {
                if (is_string($data['rest_days'])) {
                    $data['rest_days'] = array_map(
                        'trim',
                        explode(',', trim(str_replace(['"', '[', ']'], '', $data['rest_days'])))
                    );
                }
                elseif (is_array($data['rest_days'])) {
                    $data['rest_days'] = array_map(
                        'trim',
                        array_map('strval', $data['rest_days'])
                    );
                }
                else {
                    $data['rest_days'] = [];
                }
            }

            if (isset($data['subs_number']) && is_string($data['subs_number'])) {
                $data['subs_number'] = (int)$data['subs_number'];
            }

            $updatedSubscription = $this->repository->update($data, $subscription->id);

            // Commit transaction if everything is successful
            DB::commit();

            return response()->json([
                'message' => 'Subscription and client updated successfully',
                'data' => new SubscriptionResource(
                    $updatedSubscription->load(['client', 'subsType', 'trip', 'periodicity'])
                )
            ]);
        } catch (\Exception $e) {
            // Rollback transaction if an error occurs
            DB::rollBack();

            Log::error('Subscription update failed', [
                'id' => $subscription->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data ?? null
            ]);

            return response()->json([
                'message' => 'Error updating subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function destroy(Subscription $subscription): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Supprimer d'abord les transactions liées
            $subscription->transactions()->delete();

            // Ensuite supprimer l'abonnement
            $this->repository->delete($subscription->id);

            DB::commit();

            return response()->json([
                'message' => 'Subscription deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error deleting subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getByClient(int $clientId): AnonymousResourceCollection
    {
        $subscriptions = $this->repository
            ->with(['subsType', 'client.clientType', 'client.governorate', 'client.delegation', 'client.establishment', 'client.degree', 'paymentMethod', 'trip', 'periodicity'])
            ->scopeQuery(function($query) use ($clientId) {
                return $query->where('id_client', $clientId);
            })
            ->all();

        // Load only the latest transaction for each subscription
        foreach ($subscriptions as $subscription) {
            $latestTransaction = $subscription->transactions()->latest()->first();
            if ($latestTransaction) {
                $subscription->setRelation('latestTransaction', $latestTransaction);
            } else {
                $subscription->setRelation('latestTransaction', null);
            }
        }

        return SubscriptionResource::collection($subscriptions);
    }

    public function getBySubsType(int $subsTypeId): AnonymousResourceCollection
    {
        $subscriptions = $this->repository
            ->with(['subsType', 'client.clientType', 'client.governorate', 'client.delegation', 'client.establishment', 'client.degree', 'paymentMethod', 'trip', 'periodicity'])
            ->scopeQuery(function($query) use ($subsTypeId) {
                return $query->where('id_subs_type', $subsTypeId);
            })
            ->all();

        // Load only the latest transaction for each subscription
        foreach ($subscriptions as $subscription) {
            $latestTransaction = $subscription->transactions()->latest()->first();
            if ($latestTransaction) {
                $subscription->setRelation('latestTransaction', $latestTransaction);
            } else {
                $subscription->setRelation('latestTransaction', null);
            }
        }

        return SubscriptionResource::collection($subscriptions);
    }

    public function getByTrip(int $tripId): AnonymousResourceCollection
    {
        $subscriptions = $this->repository
            ->with(['subsType', 'client.clientType', 'client.governorate', 'client.delegation', 'client.establishment', 'client.degree', 'paymentMethod', 'trip', 'periodicity'])
            ->scopeQuery(function($query) use ($tripId) {
                return $query->where('id_trip', $tripId);
            })
            ->all();

        // Load only the latest transaction for each subscription
        foreach ($subscriptions as $subscription) {
            $latestTransaction = $subscription->transactions()->latest()->first();
            if ($latestTransaction) {
                $subscription->setRelation('latestTransaction', $latestTransaction);
            } else {
                $subscription->setRelation('latestTransaction', null);
            }
        }

        return SubscriptionResource::collection($subscriptions);
    }

    public function getByParent(int $parentId): AnonymousResourceCollection
    {
        $subscriptions = $this->repository
            ->with(['subsType', 'client.clientType', 'client.governorate', 'client.delegation', 'client.establishment', 'client.degree', 'paymentMethod', 'trip', 'periodicity'])
            ->scopeQuery(function($query) use ($parentId) {
                return $query->where('id_parent', $parentId);
            })
            ->all();

        // Load only the latest transaction for each subscription
        foreach ($subscriptions as $subscription) {
            $latestTransaction = $subscription->transactions()->latest()->first();
            if ($latestTransaction) {
                $subscription->setRelation('latestTransaction', $latestTransaction);
            } else {
                $subscription->setRelation('latestTransaction', null);
            }
        }

        return SubscriptionResource::collection($subscriptions);
    }

    public function getByPeriodicity(int $periodicityId): AnonymousResourceCollection
    {
        return SubscriptionResource::collection(
            $this->repository
                ->with(['subsType', 'client.clientType', 'client.governorate', 'client.delegation', 'client.establishment', 'client.degree', 'paymentMethod', 'trip', 'periodicity'])
                ->scopeQuery(function($query) use ($periodicityId) {
                    return $query->where('id_periodicity', $periodicityId);
                })
                ->all()
        );
    }


}




















