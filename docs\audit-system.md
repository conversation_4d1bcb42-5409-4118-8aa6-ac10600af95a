# Audit System Documentation

## Overview

The SRTGN backend now includes a comprehensive audit system that tracks all changes to data across all models. This system provides complete history tracking for accountability and compliance purposes.

## Features

- ✅ **Complete Model Coverage**: All models implement auditable functionality
- ✅ **Comprehensive Tracking**: Create, Update, Delete operations are tracked
- ✅ **User Attribution**: Who performed each action is recorded
- ✅ **Detailed Changes**: Before and after values are stored
- ✅ **Advanced Filtering**: Filter by model, user, event type, date range
- ✅ **Statistics & Analytics**: Audit statistics and activity reports
- ✅ **REST API**: Full API for frontend integration

## API Endpoints

### Base URL: `/api/audits`

#### 1. Get All Audits
```
GET /api/audits
```

**Query Parameters:**
- `event` - Filter by event type (created, updated, deleted, restored)
- `model_type` - Filter by model type (e.g., App\Models\Subscription)
- `user_id` - Filter by user ID
- `user_type` - Filter by user type (App\Models\Admin, App\Models\Client)
- `start_date` - Filter from date (YYYY-MM-DD)
- `end_date` - Filter to date (YYYY-MM-DD)
- `ip_address` - Filter by IP address
- `search` - Search in changes/values
- `perPage` - Items per page (default: 15, max: 100)

**Example:**
```
GET /api/audits?event=created&model_type=App\Models\Subscription&perPage=20
```

#### 2. Get Model-Specific Audits
```
GET /api/audits/model/{modelType}/{modelId}
```

**Example:**
```
GET /api/audits/model/subscription/123
```

#### 3. Get User Audits
```
GET /api/audits/user/{userId}?user_type=App\Models\Admin
```

#### 4. Get Current User's Audits
```
GET /api/audits/my-audits
```

#### 5. Get Audits by Event Type
```
GET /api/audits/event/{event}
```

**Example:**
```
GET /api/audits/event/created
```

#### 6. Get Audit Statistics
```
GET /api/audits/statistics?start_date=2024-01-01&end_date=2024-12-31
```

**Response:**
```json
{
  "data": {
    "total_audits": 1250,
    "by_event": {
      "created": 500,
      "updated": 600,
      "deleted": 150
    },
    "by_model": {
      "App\\Models\\Subscription": 400,
      "App\\Models\\Transaction": 300,
      "App\\Models\\Client": 200
    },
    "by_user_type": {
      "App\\Models\\Admin": 800,
      "App\\Models\\Client": 450
    },
    "recent_activity": {
      "2024-01-15": 45,
      "2024-01-14": 38,
      "2024-01-13": 52
    }
  }
}
```

#### 7. Get Most Active Users
```
GET /api/audits/most-active-users?limit=10&start_date=2024-01-01
```

#### 8. Get Most Audited Models
```
GET /api/audits/most-audited-models?limit=10
```

#### 9. Get Recent Activity
```
GET /api/audits/recent-activity?limit=20
```

#### 10. Get Audit Details
```
GET /api/audits/{id}
```

#### 11. Get Available Model Types
```
GET /api/audits/model-types
```

#### 12. Get Available Event Types
```
GET /api/audits/event-types
```

## Response Format

### Audit Resource Structure

```json
{
  "id": 1,
  "event": "created",
  "event_name": "Created",
  "auditable_type": "App\\Models\\Subscription",
  "auditable_id": 123,
  "model_name": "Subscription",
  "user_id": 5,
  "user_type": "App\\Models\\Admin",
  "user_name": "John Doe",
  "user": {
    "id": 5,
    "type": "admin",
    "name": "John Doe",
    "email": "<EMAIL>",
    "roles": ["AGENT"]
  },
  "auditable": {
    "id": 123,
    "type": "Subscription",
    "ref": "SUB-2024-001",
    "status": "PAYED"
  },
  "old_values": null,
  "new_values": {
    "id_client": 456,
    "status": "PAYED",
    "amount": 150.00
  },
  "changes": {
    "id_client": 456,
    "status": "PAYED",
    "amount": 150.00
  },
  "url": "/api/subscriptions",
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "tags": null,
  "created_at": "2024-01-15T10:30:00.000000Z",
  "updated_at": "2024-01-15T10:30:00.000000Z",
  "formatted_date": "2024-01-15 10:30:00",
  "human_date": "2 hours ago"
}
```

## Frontend Integration

### 1. Audit History Component

Create a component to display audit history for any model:

```javascript
// Example: Display subscription audit history
const SubscriptionAuditHistory = ({ subscriptionId }) => {
  const [audits, setAudits] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAudits();
  }, [subscriptionId]);

  const fetchAudits = async () => {
    try {
      const response = await api.get(`/audits/model/subscription/${subscriptionId}`);
      setAudits(response.data.data);
    } catch (error) {
      console.error('Error fetching audits:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="audit-history">
      <h3>History</h3>
      {audits.map(audit => (
        <div key={audit.id} className="audit-item">
          <div className="audit-header">
            <span className="event">{audit.event_name}</span>
            <span className="user">{audit.user_name}</span>
            <span className="date">{audit.human_date}</span>
          </div>
          <div className="audit-changes">
            {Object.entries(audit.changes).map(([field, change]) => (
              <div key={field} className="change-item">
                <strong>{field}:</strong>
                {audit.event === 'updated' ? (
                  <span>
                    <span className="old-value">{change.old}</span>
                    →
                    <span className="new-value">{change.new}</span>
                  </span>
                ) : (
                  <span className="new-value">{change}</span>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
```

### 2. Audit Dashboard

Create a dashboard to view system-wide audit activity:

```javascript
const AuditDashboard = () => {
  const [statistics, setStatistics] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [filters, setFilters] = useState({
    start_date: '',
    end_date: '',
    event: '',
    model_type: ''
  });

  const fetchStatistics = async () => {
    const response = await api.get('/audits/statistics', { params: filters });
    setStatistics(response.data.data);
  };

  const fetchRecentActivity = async () => {
    const response = await api.get('/audits/recent-activity?limit=50');
    setRecentActivity(response.data.data);
  };

  return (
    <div className="audit-dashboard">
      <div className="statistics-section">
        <h2>Audit Statistics</h2>
        {statistics && (
          <div className="stats-grid">
            <div className="stat-card">
              <h3>Total Audits</h3>
              <p>{statistics.total_audits}</p>
            </div>
            <div className="stat-card">
              <h3>By Event</h3>
              {Object.entries(statistics.by_event).map(([event, count]) => (
                <p key={event}>{event}: {count}</p>
              ))}
            </div>
          </div>
        )}
      </div>
      
      <div className="recent-activity-section">
        <h2>Recent Activity</h2>
        <AuditList audits={recentActivity} />
      </div>
    </div>
  );
};
```

## Database Schema

The audit system uses the `audits` table with the following structure:

```sql
CREATE TABLE audits (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_type VARCHAR(255) NULL,
  user_id BIGINT UNSIGNED NULL,
  event VARCHAR(255) NOT NULL,
  auditable_type VARCHAR(255) NOT NULL,
  auditable_id BIGINT UNSIGNED NOT NULL,
  old_values TEXT NULL,
  new_values TEXT NULL,
  url TEXT NULL,
  ip_address VARCHAR(45) NULL,
  user_agent VARCHAR(1023) NULL,
  tags VARCHAR(255) NULL,
  created_at TIMESTAMP NULL,
  updated_at TIMESTAMP NULL,
  INDEX idx_user (user_id, user_type),
  INDEX idx_auditable (auditable_type, auditable_id),
  INDEX idx_event (event),
  INDEX idx_created_at (created_at)
);
```

## Security & Permissions

- All audit endpoints require authentication (`jwt.auth` middleware)
- Users can only view audits they have permission to see
- System administrators have full access to all audit data
- Audit data is read-only - no modifications allowed

## Performance Considerations

- Audit queries are optimized with proper indexing
- Pagination is enforced (max 100 items per page)
- Large date ranges may require additional filtering
- Consider archiving old audit data for performance

## Troubleshooting

### Common Issues

1. **Missing audit data**: Ensure all models implement the Auditable interface
2. **Performance issues**: Use appropriate filters and pagination
3. **Permission errors**: Check user roles and permissions

### Debugging

Enable audit debugging by checking the `audits` table directly:

```sql
SELECT * FROM audits 
WHERE auditable_type = 'App\\Models\\Subscription' 
AND auditable_id = 123 
ORDER BY created_at DESC;
```
