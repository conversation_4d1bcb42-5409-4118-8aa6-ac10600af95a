<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AuditFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event' => 'sometimes|string|in:created,updated,deleted,restored',
            'model_type' => 'sometimes|string',
            'user_id' => 'sometimes|integer|min:1',
            'user_type' => 'sometimes|string|in:App\Models\Admin,App\Models\Client',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'ip_address' => 'sometimes|string|ip',
            'search' => 'sometimes|string|max:255',
            'perPage' => 'sometimes|integer|min:1|max:100',
            'limit' => 'sometimes|integer|min:1|max:100'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'event.in' => 'Event must be one of: created, updated, deleted, restored',
            'user_type.in' => 'User type must be either Admin or Client',
            'end_date.after_or_equal' => 'End date must be after or equal to start date',
            'ip_address.ip' => 'IP address must be a valid IP address',
            'perPage.max' => 'Per page cannot exceed 100 items',
            'limit.max' => 'Limit cannot exceed 100 items'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'perPage' => 'items per page',
            'user_id' => 'user ID',
            'model_type' => 'model type',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'ip_address' => 'IP address'
        ];
    }
}
